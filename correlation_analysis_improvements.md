# Disease vs Control Correlation Analysis Improvements

## Overview

The improved `calculate_disease_vs_control_correlation()` function better captures the four distinct patterns of gene expression dysregulation described in the paper:

1. **Intra-region, intra-phenotype**: Gene expression changes are well conserved across cell types within the same region and phenotype (e.g., PFC SFTLD vs. PFC C9FTLD)
2. **Cross-region, intra-phenotype**: Gene expression changes are conserved across cell types between regions of the same phenotype, but with weaker intra-cell-type correlations (e.g., PFC FTLD vs. MCX FTLD)
3. **Cross-phenotype, cross-region**: Almost no overlap in disease signatures when comparing ALS with FTLD across different brain regions (e.g., MCX FTLD vs. PFC ALS)
4. **Cross-phenotype, intra-region**: Cell-type-specific alterations across ALS and FTLD are conserved within the same region (e.g., MCX ALS vs. MCX FTLD)

## Key Improvements

### 1. Log Fold Change Calculation
- **Before**: Used simple ratios (disease/control)
- **After**: Uses log2 fold changes: `log2((disease + pseudocount) / (control + pseudocount))`
- **Benefit**: More robust to outliers, symmetric around zero, better statistical properties

### 2. Enhanced Data Validation
- Checks for required metadata columns
- Validates minimum sample sizes per group
- Handles missing data gracefully
- Provides informative error messages

### 3. Improved Control Reference Profiles
- Requires minimum number of samples per region-cell_type group
- Better handling of groups with insufficient samples
- More robust aggregation with sample size tracking

### 4. Comprehensive Metadata Tracking
- Automatically extracts phenotype (ALS/FTLD) and genotype (C9/Sporadic) information
- Stores analysis parameters as matrix attributes
- Tracks number of genes and samples used

### 5. Pattern-Specific Analysis Functions
- `create_ordered_correlation_matrix()`: Orders samples to highlight the four patterns
- `summarize_correlation_patterns()`: Quantifies each of the four patterns separately

## Function Parameters

### `calculate_disease_vs_control_correlation()`

- `expression_matrix`: Normalized gene expression (genes as rows, samples as columns)
- `metadata`: Sample metadata with 'Region', 'cell_type', 'Condition' columns
- `control_level`: Control condition identifier (default: "PN")
- `use_log_fold_change`: Whether to use log2 fold changes (default: TRUE)
- `min_samples_per_group`: Minimum samples per region-cell_type-condition group (default: 2)
- `pseudocount`: Small value to avoid division by zero (default: 1e-6)

### `create_ordered_correlation_matrix()`

- `correlation_matrix`: Output from `calculate_disease_vs_control_correlation()`
- `order_by`: Ordering strategy ("region_phenotype", "phenotype_region", "cell_type")

### `summarize_correlation_patterns()`

- `correlation_matrix`: Output from `calculate_disease_vs_control_correlation()`

## Expected Metadata Format

Your metadata should contain these columns:
- `Region`: Brain region (e.g., "MCX", "PFC")
- `cell_type`: Cell type identifier (e.g., "Ex_L2_3", "In_PV")
- `Condition`: Sample condition (e.g., "PN", "SALS", "C9ALS", "SFTLD", "C9FTLD")

The function automatically extracts:
- `phenotype`: "ALS" or "FTLD" based on Condition
- `genotype`: "C9" or "Sporadic" based on Condition

## Usage Example

```r
# Load required libraries
library(dplyr)
library(tidyr)
library(tibble)
library(ComplexHeatmap)

# Calculate improved correlation matrix
cor_df <- calculate_disease_vs_control_correlation(
  expression_matrix = cleaned_data[top_genes_name, ],
  metadata = colData,
  control_level = "PN",
  use_log_fold_change = TRUE,
  min_samples_per_group = 2
)

# Create ordered matrix highlighting patterns
ordered_result <- create_ordered_correlation_matrix(
  correlation_matrix = cor_df,
  order_by = "region_phenotype"
)

# Analyze the four patterns
pattern_summary <- summarize_correlation_patterns(cor_df)
print(pattern_summary$summary)

# Create enhanced heatmap
sample_metadata <- ordered_result$sample_metadata
column_ha <- HeatmapAnnotation(
  Region = sample_metadata$Region,
  Phenotype = sample_metadata$phenotype,
  Genotype = sample_metadata$genotype,
  Cell_Type = sample_metadata$cell_type
)

Heatmap(
  ordered_result$correlation_matrix,
  name = "Log2FC Correlation",
  cluster_rows = FALSE,
  cluster_columns = FALSE,
  top_annotation = column_ha,
  show_row_names = FALSE,
  show_column_names = FALSE
)
```

## Expected Results

The improved analysis should show:

1. **High correlations** within region-phenotype blocks (Pattern 1)
2. **Moderate correlations** between same phenotype across regions (Pattern 2)
3. **Low correlations** between different phenotypes across regions (Pattern 3)
4. **Moderate-to-high correlations** between different phenotypes within regions (Pattern 4)

## Troubleshooting

### Common Issues

1. **"No control samples found"**: Check that `control_level` matches values in the Condition column
2. **"Some control groups have fewer than X samples"**: Consider reducing `min_samples_per_group` or combining similar cell types
3. **"No genes with non-zero variance found"**: Check expression data normalization and filtering

### Performance Considerations

- For large datasets, consider filtering to highly variable genes first
- The correlation calculation scales quadratically with number of samples
- Memory usage is proportional to (number of samples)²

## Validation

To validate the improved method:

1. Compare pattern summary statistics with paper expectations
2. Visualize correlation matrix with proper ordering
3. Check that diagonal blocks show higher correlations than off-diagonal
4. Verify that cross-phenotype, cross-region correlations are lowest
