
# Required libraries for the function to work
library(dplyr)
library(tidyr)
library(tibble)

#' Calculate an improved correlation matrix based on log fold changes between disease and control samples.
#' This implementation better captures the four distinct patterns described in the paper:
#' 1. Intra-region, intra-phenotype conservation across cell types
#' 2. Cross-region, intra-phenotype conservation with weaker intra-cell-type correlations
#' 3. Minimal overlap between ALS and FTLD across regions
#' 4. Conserved intra-region, cell-type-specific alterations across ALS and FTLD
#'
#' @param expression_matrix A data frame or matrix of normalized gene expression (genes as rows, samples as columns).
#' @param metadata A data frame with sample metadata. Must contain columns 'Region', 'cell_type', and 'Condition'.
#'                 The rownames of metadata must match the colnames of expression_matrix.
#'                 'Condition' should contain values like "PN", "SALS", "C9ALS", "SFTLD", "C9FTLD".
#' @param control_level A character string specifying the value in the 'Condition' column that marks control samples. Defaults to "PN".
#' @param use_log_fold_change Logical, whether to use log fold changes instead of ratios. Defaults to TRUE.
#' @param min_samples_per_group Minimum number of samples required per region-cell_type-condition group. Defaults to 2.
#' @param pseudocount Small value added to avoid division by zero or log(0). Defaults to 1e-6.
#'
#' @return A square numeric matrix of the pairwise Pearson correlation between disease samples.
#'
calculate_disease_vs_control_correlation <- function(expression_matrix, metadata,
                                                   control_level = "PN",
                                                   use_log_fold_change = TRUE,
                                                   min_samples_per_group = 2,
                                                   pseudocount = 1e-6) {

  # --- 1. Prepare Data and Validate Input ---
  message("Step 1: Preparing and validating data...")

  # Ensure required columns exist
  required_cols <- c("Region", "cell_type", "Condition")
  missing_cols <- setdiff(required_cols, colnames(metadata))
  if (length(missing_cols) > 0) {
    stop("Missing required columns in metadata: ", paste(missing_cols, collapse = ", "))
  }

  # Create comprehensive identifiers
  metadata$region_celltype_id <- paste0(metadata$Region, "-", metadata$cell_type)
  metadata$region_celltype_condition_id <- paste0(metadata$Region, "-", metadata$cell_type, "-", metadata$Condition)

  # Split by condition
  is_control <- metadata$Condition == control_level
  colDataPN <- metadata[is_control, ]
  colDataDisease <- metadata[!is_control, ]

  if (nrow(colDataPN) == 0) {
    stop("No control samples found with condition: ", control_level)
  }
  if (nrow(colDataDisease) == 0) {
    stop("No disease samples found")
  }

  ctrl_group_data <- expression_matrix[, is_control, drop = FALSE]
  disease_group_data <- expression_matrix[, !is_control, drop = FALSE]

  # --- 2. Create Robust Control Reference Profiles ---
  message("Step 2: Creating robust control reference profiles...")

  # Check sample sizes per group
  control_group_sizes <- table(colDataPN$region_celltype_id)
  insufficient_groups <- names(control_group_sizes)[control_group_sizes < min_samples_per_group]

  if (length(insufficient_groups) > 0) {
    warning("Some control groups have fewer than ", min_samples_per_group, " samples: ",
            paste(insufficient_groups, collapse = ", "))
  }

  # Calculate mean control profiles with better error handling
  control_profiles <- ctrl_group_data %>%
    as.data.frame() %>%
    rownames_to_column(var = "gene_id") %>%
    pivot_longer(
      cols = -gene_id,
      names_to = "Sample_ID",
      values_to = "expression"
    ) %>%
    left_join(
      colDataPN %>%
        rownames_to_column("Sample_ID") %>%
        select(Sample_ID, region_celltype_id),
      by = "Sample_ID"
    ) %>%
    filter(!is.na(region_celltype_id)) %>%
    group_by(gene_id, region_celltype_id) %>%
    summarise(
      mean_expression = mean(expression, na.rm = TRUE),
      n_samples = n(),
      .groups = 'drop'
    ) %>%
    # Filter out groups with insufficient samples
    filter(n_samples >= min_samples_per_group) %>%
    select(-n_samples) %>%
    pivot_wider(
      names_from = region_celltype_id,
      values_from = mean_expression
    ) %>%
    column_to_rownames(var = "gene_id")

  # --- 3. Calculate Log Fold Changes for Disease Samples ---
  message("Step 3: Calculating ", ifelse(use_log_fold_change, "log fold changes", "fold changes"), " for disease samples...")

  # Add phenotype information for better tracking
  colDataDisease$phenotype <- case_when(
    grepl("ALS", colDataDisease$Condition) ~ "ALS",
    grepl("FTLD", colDataDisease$Condition) ~ "FTLD",
    TRUE ~ "Other"
  )

  results_list <- lapply(1:ncol(disease_group_data), function(i) {
    current_region_celltype <- colDataDisease$region_celltype_id[i]
    current_condition <- colDataDisease$Condition[i]
    current_phenotype <- colDataDisease$phenotype[i]
    sample_name <- rownames(colDataDisease)[i]

    if (current_region_celltype %in% colnames(control_profiles)) {
      control_profile <- control_profiles[, current_region_celltype]
      disease_profile <- disease_group_data[, i]

      # Add pseudocount to avoid division by zero
      control_profile_adj <- control_profile + pseudocount
      disease_profile_adj <- disease_profile + pseudocount

      if (use_log_fold_change) {
        # Calculate log2 fold change
        fold_change <- log2(disease_profile_adj / control_profile_adj)
      } else {
        # Calculate simple fold change (ratio)
        fold_change <- disease_profile_adj / control_profile_adj
      }

      # Remove infinite or NaN values
      fold_change[!is.finite(fold_change)] <- 0

      return(fold_change)
    } else {
      warning(paste("No matching control profile found for:", current_region_celltype,
                   "in sample:", sample_name))
      return(NULL)
    }
  })

  names(results_list) <- rownames(colDataDisease)

  # Combine into a single data frame, removing any samples that had no match
  valid_results <- Filter(Negate(is.null), results_list)

  if (length(valid_results) == 0) {
    stop("No valid disease-control comparisons could be made")
  }

  final_difference_df <- as.data.frame(do.call(cbind, valid_results))

  # --- 4. Calculate Final Correlation Matrix with Additional Metadata ---
  message("Step 4: Calculating the final correlation matrix...")

  # Remove genes with zero variance across all samples
  gene_vars <- apply(final_difference_df, 1, var, na.rm = TRUE)
  valid_genes <- !is.na(gene_vars) & gene_vars > 0

  if (sum(valid_genes) == 0) {
    stop("No genes with non-zero variance found")
  }

  final_difference_df_filtered <- final_difference_df[valid_genes, , drop = FALSE]

  # Calculate correlation matrix
  correlation_matrix <- cor(final_difference_df_filtered, use = "pairwise.complete.obs")

  # Add attributes for downstream analysis
  valid_metadata <- colDataDisease[colnames(final_difference_df), ]
  attr(correlation_matrix, "sample_metadata") <- valid_metadata
  attr(correlation_matrix, "n_genes_used") <- sum(valid_genes)
  attr(correlation_matrix, "method") <- ifelse(use_log_fold_change, "log2_fold_change", "fold_change")
  attr(correlation_matrix, "control_level") <- control_level

  message("Done. Used ", sum(valid_genes), " genes across ", ncol(final_difference_df), " disease samples.")
  return(correlation_matrix)
}

#' Create ordered correlation matrix that highlights the four distinct patterns
#' described in the paper
#'
#' @param correlation_matrix Output from calculate_disease_vs_control_correlation
#' @param order_by How to order samples. Options: "region_phenotype", "phenotype_region", "cell_type"
#'
#' @return List containing ordered correlation matrix and ordering information
#'
create_ordered_correlation_matrix <- function(correlation_matrix, order_by = "region_phenotype") {

  # Extract metadata from correlation matrix attributes
  sample_metadata <- attr(correlation_matrix, "sample_metadata")

  if (is.null(sample_metadata)) {
    stop("Correlation matrix must have sample_metadata attribute. Use calculate_disease_vs_control_correlation.")
  }

  # Create comprehensive ordering keys
  sample_metadata$phenotype <- case_when(
    grepl("ALS", sample_metadata$Condition) ~ "ALS",
    grepl("FTLD", sample_metadata$Condition) ~ "FTLD",
    TRUE ~ "Other"
  )

  sample_metadata$genotype <- case_when(
    grepl("C9", sample_metadata$Condition) ~ "C9",
    grepl("S", sample_metadata$Condition) ~ "Sporadic",
    TRUE ~ "Other"
  )

  # Define ordering based on user preference
  if (order_by == "region_phenotype") {
    # Order by Region -> Phenotype -> Genotype -> Cell Type
    sample_metadata$order_key <- paste(sample_metadata$Region,
                                     sample_metadata$phenotype,
                                     sample_metadata$genotype,
                                     sample_metadata$cell_type, sep = "_")
  } else if (order_by == "phenotype_region") {
    # Order by Phenotype -> Region -> Genotype -> Cell Type
    sample_metadata$order_key <- paste(sample_metadata$phenotype,
                                     sample_metadata$Region,
                                     sample_metadata$genotype,
                                     sample_metadata$cell_type, sep = "_")
  } else if (order_by == "cell_type") {
    # Order by Cell Type -> Region -> Phenotype -> Genotype
    sample_metadata$order_key <- paste(sample_metadata$cell_type,
                                     sample_metadata$Region,
                                     sample_metadata$phenotype,
                                     sample_metadata$genotype, sep = "_")
  } else {
    stop("order_by must be one of: 'region_phenotype', 'phenotype_region', 'cell_type'")
  }

  # Create ordering
  order_indices <- order(sample_metadata$order_key)
  ordered_samples <- rownames(sample_metadata)[order_indices]

  # Apply ordering to correlation matrix
  ordered_correlation_matrix <- correlation_matrix[ordered_samples, ordered_samples]

  # Create annotation information for visualization
  ordered_metadata <- sample_metadata[order_indices, ]

  # Create block boundaries for visualization
  block_info <- list()

  if (order_by == "region_phenotype") {
    # Create region-phenotype blocks
    region_phenotype_groups <- paste(ordered_metadata$Region, ordered_metadata$phenotype, sep = "_")
    block_boundaries <- cumsum(table(region_phenotype_groups))
    block_info$boundaries <- block_boundaries
    block_info$labels <- names(table(region_phenotype_groups))
    block_info$type <- "region_phenotype"
  } else if (order_by == "phenotype_region") {
    # Create phenotype-region blocks
    phenotype_region_groups <- paste(ordered_metadata$phenotype, ordered_metadata$Region, sep = "_")
    block_boundaries <- cumsum(table(phenotype_region_groups))
    block_info$boundaries <- block_boundaries
    block_info$labels <- names(table(phenotype_region_groups))
    block_info$type <- "phenotype_region"
  }

  return(list(
    correlation_matrix = ordered_correlation_matrix,
    sample_metadata = ordered_metadata,
    order_indices = order_indices,
    block_info = block_info,
    ordering_method = order_by
  ))
}

#' Generate summary statistics about correlation patterns
#'
#' @param correlation_matrix Output from calculate_disease_vs_control_correlation
#'
#' @return List of summary statistics
#'
summarize_correlation_patterns <- function(correlation_matrix) {

  sample_metadata <- attr(correlation_matrix, "sample_metadata")

  if (is.null(sample_metadata)) {
    stop("Correlation matrix must have sample_metadata attribute.")
  }

  # Add phenotype information
  sample_metadata$phenotype <- case_when(
    grepl("ALS", sample_metadata$Condition) ~ "ALS",
    grepl("FTLD", sample_metadata$Condition) ~ "FTLD",
    TRUE ~ "Other"
  )

  # Calculate pattern-specific correlations
  results <- list()

  # Pattern 1: Intra-region, intra-phenotype correlations
  intra_region_intra_phenotype <- c()
  for (region in unique(sample_metadata$Region)) {
    for (phenotype in unique(sample_metadata$phenotype)) {
      samples_in_group <- which(sample_metadata$Region == region &
                               sample_metadata$phenotype == phenotype)
      if (length(samples_in_group) > 1) {
        group_corr <- correlation_matrix[samples_in_group, samples_in_group]
        # Get upper triangle (excluding diagonal)
        upper_tri <- group_corr[upper.tri(group_corr)]
        intra_region_intra_phenotype <- c(intra_region_intra_phenotype, upper_tri)
      }
    }
  }

  # Pattern 2: Cross-region, intra-phenotype correlations
  cross_region_intra_phenotype <- c()
  for (phenotype in unique(sample_metadata$phenotype)) {
    phenotype_samples <- which(sample_metadata$phenotype == phenotype)
    if (length(phenotype_samples) > 1) {
      for (i in 1:(length(phenotype_samples)-1)) {
        for (j in (i+1):length(phenotype_samples)) {
          sample1 <- phenotype_samples[i]
          sample2 <- phenotype_samples[j]
          # Only include if different regions
          if (sample_metadata$Region[sample1] != sample_metadata$Region[sample2]) {
            cross_region_intra_phenotype <- c(cross_region_intra_phenotype,
                                            correlation_matrix[sample1, sample2])
          }
        }
      }
    }
  }

  # Pattern 3: Cross-phenotype, cross-region correlations (ALS vs FTLD across regions)
  cross_phenotype_cross_region <- c()
  als_samples <- which(sample_metadata$phenotype == "ALS")
  ftld_samples <- which(sample_metadata$phenotype == "FTLD")

  for (als_idx in als_samples) {
    for (ftld_idx in ftld_samples) {
      # Only include if different regions
      if (sample_metadata$Region[als_idx] != sample_metadata$Region[ftld_idx]) {
        cross_phenotype_cross_region <- c(cross_phenotype_cross_region,
                                        correlation_matrix[als_idx, ftld_idx])
      }
    }
  }

  # Pattern 4: Cross-phenotype, intra-region correlations (ALS vs FTLD within regions)
  cross_phenotype_intra_region <- c()
  for (region in unique(sample_metadata$Region)) {
    region_als <- which(sample_metadata$Region == region & sample_metadata$phenotype == "ALS")
    region_ftld <- which(sample_metadata$Region == region & sample_metadata$phenotype == "FTLD")

    for (als_idx in region_als) {
      for (ftld_idx in region_ftld) {
        cross_phenotype_intra_region <- c(cross_phenotype_intra_region,
                                        correlation_matrix[als_idx, ftld_idx])
      }
    }
  }

  # Compile results
  results$pattern_1_intra_region_intra_phenotype <- list(
    values = intra_region_intra_phenotype,
    mean = mean(intra_region_intra_phenotype, na.rm = TRUE),
    median = median(intra_region_intra_phenotype, na.rm = TRUE),
    n = length(intra_region_intra_phenotype)
  )

  results$pattern_2_cross_region_intra_phenotype <- list(
    values = cross_region_intra_phenotype,
    mean = mean(cross_region_intra_phenotype, na.rm = TRUE),
    median = median(cross_region_intra_phenotype, na.rm = TRUE),
    n = length(cross_region_intra_phenotype)
  )

  results$pattern_3_cross_phenotype_cross_region <- list(
    values = cross_phenotype_cross_region,
    mean = mean(cross_phenotype_cross_region, na.rm = TRUE),
    median = median(cross_phenotype_cross_region, na.rm = TRUE),
    n = length(cross_phenotype_cross_region)
  )

  results$pattern_4_cross_phenotype_intra_region <- list(
    values = cross_phenotype_intra_region,
    mean = mean(cross_phenotype_intra_region, na.rm = TRUE),
    median = median(cross_phenotype_intra_region, na.rm = TRUE),
    n = length(cross_phenotype_intra_region)
  )

  # Overall summary
  results$summary <- data.frame(
    Pattern = c("Intra-region, Intra-phenotype",
                "Cross-region, Intra-phenotype",
                "Cross-phenotype, Cross-region",
                "Cross-phenotype, Intra-region"),
    Mean_Correlation = c(results$pattern_1_intra_region_intra_phenotype$mean,
                        results$pattern_2_cross_region_intra_phenotype$mean,
                        results$pattern_3_cross_phenotype_cross_region$mean,
                        results$pattern_4_cross_phenotype_intra_region$mean),
    N_Comparisons = c(results$pattern_1_intra_region_intra_phenotype$n,
                     results$pattern_2_cross_region_intra_phenotype$n,
                     results$pattern_3_cross_phenotype_cross_region$n,
                     results$pattern_4_cross_phenotype_intra_region$n)
  )

  return(results)
}
# Example usage with improved correlation calculation
# Assuming you have: cleaned_data (expression matrix), colData (metadata), top_genes, adata_mat

# Step 1: Calculate improved disease vs control correlation
top_genes_name <- intersect(rownames(adata_mat)[top_genes], rownames(cleaned_data))
cor_df <- calculate_disease_vs_control_correlation(
  expression_matrix = cleaned_data[top_genes_name, ],
  metadata = colData,
  control_level = "PN",
  use_log_fold_change = TRUE,  # Use log2 fold changes instead of ratios
  min_samples_per_group = 2,   # Require at least 2 samples per group
  pseudocount = 1e-6          # Small value to avoid log(0)
)

# Step 2: Create ordered correlation matrix to highlight patterns
ordered_result <- create_ordered_correlation_matrix(
  correlation_matrix = cor_df,
  order_by = "region_phenotype"  # Order by region first, then phenotype
)

# Step 3: Generate summary statistics about the four patterns
pattern_summary <- summarize_correlation_patterns(cor_df)
print("Pattern Summary:")
print(pattern_summary$summary)

# Step 4: Create improved heatmap visualization
library(ComplexHeatmap)

# Create annotation for the heatmap
sample_metadata <- ordered_result$sample_metadata
column_ha <- HeatmapAnnotation(
  Region = sample_metadata$Region,
  Phenotype = sample_metadata$phenotype,
  Genotype = sample_metadata$genotype,
  Cell_Type = sample_metadata$cell_type,
  col = list(
    Region = c("MCX" = "red", "PFC" = "blue"),
    Phenotype = c("ALS" = "orange", "FTLD" = "purple"),
    Genotype = c("C9" = "darkgreen", "Sporadic" = "lightgreen")
  ),
  annotation_name_gp = gpar(fontsize = 8),
  simple_anno_size = unit(0.3, "cm")
)

# Create the heatmap
Heatmap(
  ordered_result$correlation_matrix,
  name = "Log2FC Correlation",
  col = circlize::colorRamp2(c(-1, 0, 1), c("blue", "white", "red")),

  # --- Key Parameters ---
  cluster_rows = FALSE,
  cluster_columns = FALSE,
  top_annotation = column_ha,
  show_row_names = FALSE,
  show_column_names = FALSE,

  # Add block boundaries if available
  row_split = if(!is.null(ordered_result$block_info$boundaries)) {
    rep(1:length(ordered_result$block_info$boundaries),
        diff(c(0, ordered_result$block_info$boundaries)))
  } else NULL,
  column_split = if(!is.null(ordered_result$block_info$boundaries)) {
    rep(1:length(ordered_result$block_info$boundaries),
        diff(c(0, ordered_result$block_info$boundaries)))
  } else NULL,

  # Aesthetics
  row_title = "Disease Samples",
  column_title = "Disease Samples",
  heatmap_legend_param = list(
    title = "Pearson Correlation\n(Log2 Fold Changes)",
    title_gp = gpar(fontsize = 10),
    labels_gp = gpar(fontsize = 8)
  ),

  # Add information about the analysis
  column_title_gp = gpar(fontsize = 12),
  row_title_gp = gpar(fontsize = 12)
)

# Optional: Save pattern summary to file
# write.csv(pattern_summary$summary, "correlation_pattern_summary.csv", row.names = FALSE)

#' Diagnostic function to identify why condition effects are weak
#' This function helps identify potential problems in the correlation analysis
#'
#' @param expression_matrix Expression matrix used in correlation analysis
#' @param metadata Metadata used in correlation analysis
#' @param control_level Control condition identifier
#'
#' @return List of diagnostic information
#'
diagnose_correlation_problems <- function(expression_matrix, metadata, control_level = "PN") {

  cat("=== DIAGNOSTIC ANALYSIS FOR WEAK CONDITION EFFECTS ===\n\n")

  # Problem 1: Check if disease effects are being averaged out
  cat("1. CHECKING SAMPLE COMPOSITION AND AGGREGATION\n")
  cat("----------------------------------------------\n")

  metadata$region_celltype_id <- paste0(metadata$Region, "-", metadata$cell_type)

  # Check sample sizes per group
  sample_composition <- metadata %>%
    group_by(Region, cell_type, Condition) %>%
    summarise(n_samples = n(), .groups = 'drop') %>%
    arrange(Region, cell_type, Condition)

  print(sample_composition)

  # Check if control groups are too large compared to disease groups
  control_sizes <- sample_composition %>% filter(Condition == control_level)
  disease_sizes <- sample_composition %>% filter(Condition != control_level)

  cat("\nControl group sizes (mean ± sd):",
      round(mean(control_sizes$n_samples), 2), "±",
      round(sd(control_sizes$n_samples), 2), "\n")
  cat("Disease group sizes (mean ± sd):",
      round(mean(disease_sizes$n_samples), 2), "±",
      round(sd(disease_sizes$n_samples), 2), "\n")

  # Problem 2: Check gene filtering and variance
  cat("\n2. CHECKING GENE FILTERING AND VARIANCE\n")
  cat("---------------------------------------\n")

  # Calculate variance within controls vs within disease
  is_control <- metadata$Condition == control_level
  ctrl_data <- expression_matrix[, is_control]
  disease_data <- expression_matrix[, !is_control]

  gene_var_ctrl <- apply(ctrl_data, 1, var, na.rm = TRUE)
  gene_var_disease <- apply(disease_data, 1, var, na.rm = TRUE)

  cat("Genes with zero variance in controls:", sum(gene_var_ctrl == 0, na.rm = TRUE), "\n")
  cat("Genes with zero variance in disease:", sum(gene_var_disease == 0, na.rm = TRUE), "\n")
  cat("Mean gene variance in controls:", round(mean(gene_var_ctrl, na.rm = TRUE), 4), "\n")
  cat("Mean gene variance in disease:", round(mean(gene_var_disease, na.rm = TRUE), 4), "\n")

  # Problem 3: Check magnitude of disease effects
  cat("\n3. CHECKING MAGNITUDE OF DISEASE EFFECTS\n")
  cat("----------------------------------------\n")

  # Calculate fold changes for each region-cell_type combination
  fold_changes_summary <- list()

  for (region_celltype in unique(metadata$region_celltype_id)) {
    ctrl_samples <- which(metadata$region_celltype_id == region_celltype &
                         metadata$Condition == control_level)
    disease_samples <- which(metadata$region_celltype_id == region_celltype &
                           metadata$Condition != control_level)

    if (length(ctrl_samples) > 0 & length(disease_samples) > 0) {
      ctrl_mean <- rowMeans(expression_matrix[, ctrl_samples, drop = FALSE])
      disease_mean <- rowMeans(expression_matrix[, disease_samples, drop = FALSE])

      # Calculate log2 fold changes
      log2fc <- log2((disease_mean + 1e-6) / (ctrl_mean + 1e-6))

      fold_changes_summary[[region_celltype]] <- list(
        mean_abs_log2fc = mean(abs(log2fc), na.rm = TRUE),
        median_abs_log2fc = median(abs(log2fc), na.rm = TRUE),
        n_upregulated = sum(log2fc > 0.5, na.rm = TRUE),  # >1.4 fold up
        n_downregulated = sum(log2fc < -0.5, na.rm = TRUE), # >1.4 fold down
        max_log2fc = max(log2fc, na.rm = TRUE),
        min_log2fc = min(log2fc, na.rm = TRUE)
      )
    }
  }

  # Summarize fold changes
  if (length(fold_changes_summary) > 0) {
    mean_effects <- sapply(fold_changes_summary, function(x) x$mean_abs_log2fc)
    cat("Mean absolute log2FC across region-cell_type groups:",
        round(mean(mean_effects, na.rm = TRUE), 4), "\n")
    cat("Range of mean absolute log2FC:",
        round(min(mean_effects, na.rm = TRUE), 4), "to",
        round(max(mean_effects, na.rm = TRUE), 4), "\n")

    total_up <- sum(sapply(fold_changes_summary, function(x) x$n_upregulated))
    total_down <- sum(sapply(fold_changes_summary, function(x) x$n_downregulated))
    cat("Total upregulated genes (>1.4x):", total_up, "\n")
    cat("Total downregulated genes (>1.4x):", total_down, "\n")
  }

  # Problem 4: Check for batch effects or confounders
  cat("\n4. CHECKING FOR POTENTIAL CONFOUNDERS\n")
  cat("-------------------------------------\n")

  # Check if conditions are confounded with other variables
  if ("batch" %in% colnames(metadata)) {
    batch_condition_table <- table(metadata$batch, metadata$Condition)
    cat("Batch-Condition crosstab:\n")
    print(batch_condition_table)
  }

  if ("donor_id" %in% colnames(metadata) || "individual" %in% colnames(metadata)) {
    donor_col <- ifelse("donor_id" %in% colnames(metadata), "donor_id", "individual")
    donor_condition_table <- table(metadata[[donor_col]], metadata$Condition)
    cat("Donor-Condition crosstab (showing first 10 donors):\n")
    print(donor_condition_table[1:min(10, nrow(donor_condition_table)), ])
  }

  # Problem 5: Check normalization issues
  cat("\n5. CHECKING NORMALIZATION AND SCALING\n")
  cat("-------------------------------------\n")

  # Check expression distributions
  sample_means <- colMeans(expression_matrix, na.rm = TRUE)
  sample_vars <- apply(expression_matrix, 2, var, na.rm = TRUE)

  cat("Sample mean expression range:",
      round(min(sample_means, na.rm = TRUE), 2), "to",
      round(max(sample_means, na.rm = TRUE), 2), "\n")
  cat("Sample variance range:",
      round(min(sample_vars, na.rm = TRUE), 2), "to",
      round(max(sample_vars, na.rm = TRUE), 2), "\n")

  # Check if controls and disease have similar distributions
  ctrl_sample_means <- sample_means[is_control]
  disease_sample_means <- sample_means[!is_control]

  cat("Control sample means:", round(mean(ctrl_sample_means), 2), "±",
      round(sd(ctrl_sample_means), 2), "\n")
  cat("Disease sample means:", round(mean(disease_sample_means), 2), "±",
      round(sd(disease_sample_means), 2), "\n")

  # Problem 6: Check if using appropriate genes
  cat("\n6. CHECKING GENE SELECTION\n")
  cat("--------------------------\n")

  cat("Total genes in expression matrix:", nrow(expression_matrix), "\n")

  # Check if highly variable genes are being used
  gene_vars_overall <- apply(expression_matrix, 1, var, na.rm = TRUE)
  cat("Genes with variance > 1:", sum(gene_vars_overall > 1, na.rm = TRUE), "\n")
  cat("Genes with variance > 0.1:", sum(gene_vars_overall > 0.1, na.rm = TRUE), "\n")
  cat("Mean gene variance:", round(mean(gene_vars_overall, na.rm = TRUE), 4), "\n")

  cat("\n=== RECOMMENDATIONS ===\n")
  cat("1. If disease effects are small: Use more stringent gene filtering (highly variable genes)\n")
  cat("2. If sample sizes are imbalanced: Consider subsampling or weighted analysis\n")
  cat("3. If batch effects present: Apply batch correction before correlation analysis\n")
  cat("4. If normalization issues: Re-normalize data or use rank-based methods\n")
  cat("5. Consider using individual-level analysis instead of pseudo-bulk aggregation\n\n")

  # Return diagnostic data
  return(list(
    sample_composition = sample_composition,
    fold_changes_summary = fold_changes_summary,
    gene_variances = list(
      control = gene_var_ctrl,
      disease = gene_var_disease,
      overall = gene_vars_overall
    ),
    sample_stats = list(
      means = sample_means,
      variances = sample_vars
    )
  ))
}

#' Alternative correlation calculation that preserves individual-level variation
#' This approach doesn't aggregate controls, preserving disease-specific signals
#'
#' @param expression_matrix Expression matrix (genes x samples)
#' @param metadata Sample metadata
#' @param control_level Control condition identifier
#' @param use_individual_controls Whether to use individual controls instead of aggregated means
#' @param filter_low_variance Whether to filter genes with low variance
#' @param variance_threshold Minimum variance threshold for gene filtering
#'
#' @return Correlation matrix
#'
calculate_individual_level_correlation <- function(expression_matrix, metadata,
                                                 control_level = "PN",
                                                 use_individual_controls = TRUE,
                                                 filter_low_variance = TRUE,
                                                 variance_threshold = 0.1) {

  message("Calculating individual-level disease vs control correlation...")

  # Prepare metadata
  metadata$region_celltype_id <- paste0(metadata$Region, "-", metadata$cell_type)

  # Split samples
  is_control <- metadata$Condition == control_level
  control_metadata <- metadata[is_control, ]
  disease_metadata <- metadata[!is_control, ]

  if (sum(is_control) == 0 || sum(!is_control) == 0) {
    stop("Need both control and disease samples")
  }

  # Filter genes by variance if requested
  if (filter_low_variance) {
    gene_vars <- apply(expression_matrix, 1, var, na.rm = TRUE)
    high_var_genes <- gene_vars > variance_threshold & !is.na(gene_vars)
    expression_matrix <- expression_matrix[high_var_genes, ]
    message("Filtered to ", sum(high_var_genes), " high-variance genes")
  }

  # Calculate fold changes for each disease sample
  fold_change_matrix <- matrix(NA,
                              nrow = nrow(expression_matrix),
                              ncol = sum(!is_control),
                              dimnames = list(rownames(expression_matrix),
                                            rownames(disease_metadata)))

  for (i in 1:ncol(fold_change_matrix)) {
    disease_sample <- rownames(disease_metadata)[i]
    disease_region_celltype <- disease_metadata$region_celltype_id[i]

    # Find matching controls
    matching_controls <- which(control_metadata$region_celltype_id == disease_region_celltype)

    if (length(matching_controls) > 0) {
      disease_profile <- expression_matrix[, disease_sample]

      if (use_individual_controls && length(matching_controls) > 1) {
        # Use the median control as reference (more robust than mean)
        control_profiles <- expression_matrix[, rownames(control_metadata)[matching_controls]]
        control_reference <- apply(control_profiles, 1, median, na.rm = TRUE)
      } else {
        # Use mean of controls
        control_profiles <- expression_matrix[, rownames(control_metadata)[matching_controls], drop = FALSE]
        control_reference <- rowMeans(control_profiles, na.rm = TRUE)
      }

      # Calculate log2 fold change
      fold_change_matrix[, i] <- log2((disease_profile + 1e-6) / (control_reference + 1e-6))
    } else {
      warning("No matching controls for sample: ", disease_sample)
    }
  }

  # Remove samples with no matching controls
  valid_samples <- !is.na(colSums(fold_change_matrix))
  fold_change_matrix <- fold_change_matrix[, valid_samples, drop = FALSE]
  valid_disease_metadata <- disease_metadata[valid_samples, ]

  # Calculate correlation matrix
  correlation_matrix <- cor(fold_change_matrix, use = "pairwise.complete.obs")

  # Add metadata attributes
  attr(correlation_matrix, "sample_metadata") <- valid_disease_metadata
  attr(correlation_matrix, "method") <- "individual_level_log2fc"
  attr(correlation_matrix, "n_genes_used") <- nrow(fold_change_matrix)
  attr(correlation_matrix, "variance_threshold") <- variance_threshold

  message("Done. Used ", nrow(fold_change_matrix), " genes across ",
          ncol(fold_change_matrix), " disease samples.")

  return(correlation_matrix)
}

