# Test script for improved disease vs control correlation analysis
# This script creates synthetic data to test the improved functions

library(dplyr)
library(tidyr)
library(tibble)

# Source the improved functions
source("correlation_disease_control.R")

# Create synthetic test data
set.seed(42)

# Parameters
n_genes <- 1000
n_samples_per_group <- 5
regions <- c("MCX", "PFC")
cell_types <- c("Ex_L2_3", "Ex_L5", "In_PV", "In_SST")
conditions <- c("PN", "SALS", "C9ALS", "SFTLD", "C9FTLD")

# Create metadata
metadata_list <- list()
sample_counter <- 1

for (region in regions) {
  for (cell_type in cell_types) {
    for (condition in conditions) {
      for (i in 1:n_samples_per_group) {
        metadata_list[[sample_counter]] <- data.frame(
          Sample_ID = paste0("Sample_", sample_counter),
          Region = region,
          cell_type = cell_type,
          Condition = condition,
          stringsAsFactors = FALSE
        )
        sample_counter <- sample_counter + 1
      }
    }
  }
}

test_metadata <- do.call(rbind, metadata_list)
rownames(test_metadata) <- test_metadata$Sample_ID

# Create synthetic expression data
n_samples <- nrow(test_metadata)
gene_names <- paste0("Gene_", 1:n_genes)

# Base expression levels
base_expression <- matrix(
  rnorm(n_genes * n_samples, mean = 5, sd = 1),
  nrow = n_genes,
  ncol = n_samples,
  dimnames = list(gene_names, test_metadata$Sample_ID)
)

# Add disease-specific effects to simulate the four patterns
for (i in 1:n_samples) {
  sample_info <- test_metadata[i, ]
  
  # Pattern 1: Intra-region, intra-phenotype effects
  if (sample_info$Condition != "PN") {
    phenotype <- ifelse(grepl("ALS", sample_info$Condition), "ALS", "FTLD")
    region_phenotype_effect <- rnorm(n_genes, mean = 0, sd = 0.5)
    base_expression[, i] <- base_expression[, i] + region_phenotype_effect
  }
  
  # Pattern 2: Cross-region effects (weaker)
  if (sample_info$Condition != "PN") {
    phenotype_effect <- rnorm(n_genes, mean = 0, sd = 0.3)
    base_expression[, i] <- base_expression[, i] + phenotype_effect
  }
  
  # Pattern 4: Cell-type-specific effects within region
  if (sample_info$Condition != "PN") {
    cell_type_effect <- rnorm(n_genes, mean = 0, sd = 0.2)
    base_expression[, i] <- base_expression[, i] + cell_type_effect
  }
}

# Ensure positive expression values
test_expression <- pmax(base_expression, 0.1)

cat("Created synthetic test data:\n")
cat("- Genes:", n_genes, "\n")
cat("- Samples:", n_samples, "\n")
cat("- Regions:", length(regions), "\n")
cat("- Cell types:", length(cell_types), "\n")
cat("- Conditions:", length(conditions), "\n\n")

# Test the improved correlation function
cat("Testing improved correlation calculation...\n")

tryCatch({
  # Test with default parameters
  cor_result <- calculate_disease_vs_control_correlation(
    expression_matrix = test_expression,
    metadata = test_metadata,
    control_level = "PN",
    use_log_fold_change = TRUE,
    min_samples_per_group = 2
  )
  
  cat("✓ Basic correlation calculation successful\n")
  cat("- Correlation matrix dimensions:", dim(cor_result), "\n")
  cat("- Number of genes used:", attr(cor_result, "n_genes_used"), "\n")
  cat("- Method:", attr(cor_result, "method"), "\n\n")
  
  # Test ordering function
  cat("Testing correlation matrix ordering...\n")
  
  ordered_result <- create_ordered_correlation_matrix(
    correlation_matrix = cor_result,
    order_by = "region_phenotype"
  )
  
  cat("✓ Matrix ordering successful\n")
  cat("- Ordering method:", ordered_result$ordering_method, "\n")
  cat("- Block info available:", !is.null(ordered_result$block_info), "\n\n")
  
  # Test pattern analysis
  cat("Testing pattern analysis...\n")
  
  pattern_summary <- summarize_correlation_patterns(cor_result)
  
  cat("✓ Pattern analysis successful\n")
  print(pattern_summary$summary)
  cat("\n")
  
  # Validate expected patterns
  cat("Validating expected patterns...\n")
  
  # Pattern 1 should have highest correlations
  pattern1_mean <- pattern_summary$pattern_1_intra_region_intra_phenotype$mean
  pattern2_mean <- pattern_summary$pattern_2_cross_region_intra_phenotype$mean
  pattern3_mean <- pattern_summary$pattern_3_cross_phenotype_cross_region$mean
  pattern4_mean <- pattern_summary$pattern_4_cross_phenotype_intra_region$mean
  
  if (pattern1_mean > pattern2_mean) {
    cat("✓ Pattern 1 > Pattern 2 (as expected)\n")
  } else {
    cat("⚠ Pattern 1 ≤ Pattern 2 (unexpected)\n")
  }
  
  if (pattern3_mean < pattern4_mean) {
    cat("✓ Pattern 3 < Pattern 4 (as expected)\n")
  } else {
    cat("⚠ Pattern 3 ≥ Pattern 4 (unexpected)\n")
  }
  
  if (pattern3_mean < pattern1_mean) {
    cat("✓ Pattern 3 < Pattern 1 (as expected)\n")
  } else {
    cat("⚠ Pattern 3 ≥ Pattern 1 (unexpected)\n")
  }
  
  cat("\nAll tests completed successfully!\n")
  
  # Optional: Create a simple visualization test
  if (require(ComplexHeatmap, quietly = TRUE)) {
    cat("\nCreating test heatmap...\n")
    
    sample_metadata <- ordered_result$sample_metadata
    column_ha <- HeatmapAnnotation(
      Region = sample_metadata$Region,
      Phenotype = sample_metadata$phenotype,
      Genotype = sample_metadata$genotype,
      col = list(
        Region = c("MCX" = "red", "PFC" = "blue"),
        Phenotype = c("ALS" = "orange", "FTLD" = "purple"),
        Genotype = c("C9" = "darkgreen", "Sporadic" = "lightgreen")
      )
    )
    
    # Create a small test heatmap (first 20 samples for visibility)
    n_show <- min(20, nrow(ordered_result$correlation_matrix))
    test_heatmap <- Heatmap(
      ordered_result$correlation_matrix[1:n_show, 1:n_show],
      name = "Test Correlation",
      cluster_rows = FALSE,
      cluster_columns = FALSE,
      top_annotation = column_ha,
      show_row_names = FALSE,
      show_column_names = FALSE,
      column_title = "Test Correlation Matrix (First 20 samples)"
    )
    
    cat("✓ Test heatmap created successfully\n")
  }
  
} catch(error) {
  cat("✗ Error during testing:", error$message, "\n")
  cat("Please check your data and function implementation.\n")
})

cat("\nTest script completed.\n")
